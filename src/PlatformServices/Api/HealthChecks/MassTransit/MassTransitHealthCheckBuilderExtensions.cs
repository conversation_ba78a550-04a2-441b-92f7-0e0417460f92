using System.Collections.Generic;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Api.HealthChecks.MassTransit;

public static class MassTransitHealthCheckBuilderExtensions
{
    private const string Name = "masstransit";

    public static IHealthChecksBuilder AddMassTransit(
        this IHealthChecksBuilder builder,
        string name = null,
        IEnumerable<string> tags = null)
    {
        builder.Add(
            new HealthCheckRegistration(
                name ?? Name,
                factory: sp =>
                {
                    var busControl = sp.GetService<IBusControl>();
                    return new MassTransitBusHealthCheck(busControl);
                },
                failureStatus: HealthStatus.Unhealthy,
                tags: tags)
            );
            
        return builder;
    }
}
