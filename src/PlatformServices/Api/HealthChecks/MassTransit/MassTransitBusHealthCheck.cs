using System;
using System.Threading;
using System.Threading.Tasks;
using MassTransit;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Api.HealthChecks.MassTransit;

public class MassTransitBusHealthCheck : IHealthCheck 
{ 
    private readonly IBusControl _busControl; 
 
    public MassTransitBusHealthCheck(IBusControl busControl)
    {
        _busControl = busControl; 
    } 
 
    public Task<HealthCheckResult> CheckHealthAsync( 
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var health = _busControl.CheckHealth();

            return Task.FromResult(health.Status switch
            {
                BusHealthStatus.Unhealthy => HealthCheckResult.Unhealthy(),
                BusHealthStatus.Degraded => HealthCheckResult.Degraded(),
                BusHealthStatus.Healthy => HealthCheckResult.Healthy(),
                _ => throw new ArgumentOutOfRangeException()
            });
        }
        catch (Exception e)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy());
        }
    } 
}
